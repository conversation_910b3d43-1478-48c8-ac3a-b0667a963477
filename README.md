# IT Request Form System

A comprehensive IT request management system built with PHP, Bootstrap, and MariaDB. This system allows employees to submit IT requests, provides a two-stage approval workflow (Department Head → IT Lead), and includes status tracking with timeline visualization.

## Features

### Core Functionality
- **User Authentication**: Role-based access control (<PERSON><PERSON><PERSON><PERSON>, Department Head, IT Lead, Admin)
- **Request Management**: Submit, track, and manage IT requests
- **Category System**: Organize requests by type (Software, Hardware, Access, Maintenance, Other)
- **Priority Levels**: Low, Medium, High, Urgent
- **File Attachments**: Upload supporting documents

### Approval Workflow
- **Two-Stage Approval**: Department Head approval followed by IT Lead approval
- **Status Tracking**: Real-time status updates with timeline view
- **Comments System**: Internal and external comments for communication
- **Email Notifications**: Automated notifications for status changes (configurable)

### User Roles
- **Employee**: Submit and track own requests
- **Department Head**: Approve requests from their department
- **IT Lead**: Final approval and implementation management
- **Admin**: Full system access and user management

### Dashboard & Reporting
- **Personal Dashboard**: Overview of user's requests and pending approvals
- **Status Timeline**: Visual timeline showing request progress
- **Search & Filtering**: Advanced filtering by status, priority, category, etc.
- **Admin Panel**: User management, system configuration, and reporting

## Technology Stack

- **Backend**: PHP 7.4+
- **Database**: MariaDB/MySQL
- **Frontend**: Bootstrap 5.3, HTML5, CSS3, JavaScript
- **Icons**: Bootstrap Icons
- **Architecture**: MVC-inspired structure with separate classes for business logic

## Installation

### Prerequisites
- Web server (Apache/Nginx)
- PHP 7.4 or higher
- MariaDB/MySQL 5.7 or higher
- mod_rewrite enabled (for clean URLs)

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   git clone <repository-url>
   cd it-request-form
   ```

2. **Database Setup**
   - Create a new database in MariaDB/MySQL
   - Import the database schema:
     ```sql
     mysql -u username -p database_name < database/schema.sql
     ```
   - Import sample data (optional):
     ```sql
     mysql -u username -p database_name < database/sample_data.sql
     ```

3. **Configuration**
   - Edit `config/database.php` with your database credentials:
     ```php
     private $host = 'localhost';
     private $db_name = 'it_request_system';
     private $username = 'your_username';
     private $password = 'your_password';
     ```
   - Update `config/config.php` with your application settings:
     - Base URL
     - Email settings (SMTP configuration)
     - File upload settings

4. **File Permissions**
   ```bash
   chmod 755 uploads/
   chmod 644 config/*.php
   ```

5. **Web Server Configuration**
   - Point your web server document root to the project directory
   - Ensure mod_rewrite is enabled for Apache
   - Configure virtual host if needed

## Default Login Credentials

After importing the sample data, you can use these credentials:

| Role | Username | Password | Description |
|------|----------|----------|-------------|
| Admin | admin | password123 | System administrator |
| IT Lead | it_lead | password123 | IT department lead |
| Dept Head | hr_head | password123 | HR department head |
| Employee | jdoe | password123 | Regular employee |

**Important**: Change these passwords immediately after installation!

## File Structure

```
it-request-form/
├── admin/                  # Admin panel pages
├── assets/                 # CSS, JS, and static assets
│   ├── css/
│   └── js/
├── classes/                # PHP classes (MVC-style)
│   ├── User.php
│   ├── Request.php
│   ├── Category.php
│   └── Approval.php
├── config/                 # Configuration files
│   ├── config.php
│   └── database.php
├── database/               # Database schema and sample data
│   ├── schema.sql
│   └── sample_data.sql
├── includes/               # Shared templates
│   ├── header.php
│   └── footer.php
├── uploads/                # File upload directory
├── *.php                   # Main application pages
└── README.md
```

## Usage Guide

### For Employees
1. **Login** with your credentials
2. **Submit Request**: Click "New Request" and fill out the form
3. **Track Progress**: View your requests in "My Requests"
4. **Add Comments**: Communicate with approvers through comments

### For Department Heads
1. **Review Requests**: Check "Department Approvals" for pending requests
2. **Approve/Reject**: Review details and make approval decisions
3. **Add Comments**: Provide feedback or additional information

### For IT Leads
1. **Final Approval**: Review department-approved requests in "IT Approvals"
2. **Manage Implementation**: Update request status as work progresses
3. **View All Requests**: Monitor system-wide request activity

### For Administrators
1. **User Management**: Add/edit users and assign roles
2. **System Configuration**: Manage departments and categories
3. **Reporting**: Generate reports and monitor system usage

## Security Features

- **Password Hashing**: Secure password storage using PHP's password_hash()
- **SQL Injection Protection**: Prepared statements throughout
- **XSS Prevention**: Input sanitization and output escaping
- **File Upload Security**: Type and size validation
- **Role-Based Access**: Granular permission system
- **Session Management**: Secure session handling

## Customization

### Adding New Request Categories
1. Access Admin Panel → Manage Categories
2. Add new category with appropriate type
3. Categories are automatically available in request forms

### Modifying Approval Workflow
- Edit `classes/Approval.php` to customize approval logic
- Modify status definitions in `config/config.php`
- Update UI elements in approval pages

### Email Notifications
- Configure SMTP settings in `config/config.php`
- Implement email sending in approval workflow methods
- Customize email templates as needed

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify database credentials in `config/database.php`
   - Ensure database server is running
   - Check database name and user permissions

2. **File Upload Issues**
   - Check `uploads/` directory permissions (755)
   - Verify PHP upload settings (upload_max_filesize, post_max_size)
   - Ensure web server has write access to uploads directory

3. **Permission Denied Errors**
   - Verify user roles are correctly assigned
   - Check session data for proper role information
   - Review role-based access control logic

4. **Styling Issues**
   - Ensure Bootstrap CSS/JS files are loading
   - Check custom CSS file path
   - Verify internet connection for CDN resources

## Support

For technical support or feature requests:
1. Check the troubleshooting section above
2. Review the code documentation in PHP files
3. Contact your system administrator

## License

This project is developed for internal use. Please ensure compliance with your organization's software policies.

## Version History

- **v1.0.0**: Initial release with core functionality
  - User authentication and role management
  - Request submission and approval workflow
  - Status tracking and timeline view
  - Basic admin panel and reporting
