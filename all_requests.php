<?php
require_once 'config/config.php';
require_once 'classes/Request.php';
require_once 'classes/Category.php';

// Require IT lead or admin role
require_role(['it_lead', 'admin']);

$page_title = 'All Requests';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Handle filters
$filters = [];
if (!empty($_GET['status'])) {
    $filters['status'] = $_GET['status'];
}
if (!empty($_GET['priority'])) {
    $filters['priority'] = $_GET['priority'];
}
if (!empty($_GET['category_id'])) {
    $filters['category_id'] = $_GET['category_id'];
}
if (!empty($_GET['search'])) {
    $filters['search'] = $_GET['search'];
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * RECORDS_PER_PAGE;

// Get requests
$request = new Request($db);
$requests = $request->getAll(RECORDS_PER_PAGE, $offset, $filters);
$total_requests = $request->getCount($filters);
$total_pages = ceil($total_requests / RECORDS_PER_PAGE);

// Get categories for filter
$category = new Category($db);
$categories = $category->getActive();

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-folder me-2"></i>
            All Requests
        </h1>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                               placeholder="Request number, title, or description">
                    </div>
                    
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Statuses</option>
                            <?php foreach (REQUEST_STATUSES as $key => $label): ?>
                                <option value="<?php echo $key; ?>" 
                                        <?php echo (isset($_GET['status']) && $_GET['status'] == $key) ? 'selected' : ''; ?>>
                                    <?php echo $label; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label for="priority" class="form-label">Priority</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="">All Priorities</option>
                            <?php foreach (PRIORITY_LEVELS as $key => $label): ?>
                                <option value="<?php echo $key; ?>" 
                                        <?php echo (isset($_GET['priority']) && $_GET['priority'] == $key) ? 'selected' : ''; ?>>
                                    <?php echo $label; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="category_id" class="form-label">Category</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo $cat['id']; ?>" 
                                        <?php echo (isset($_GET['category_id']) && $_GET['category_id'] == $cat['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search"></i> Filter
                        </button>
                        <a href="all_requests.php" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Results -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-table me-2"></i>
                    Requests (<?php echo number_format($total_requests); ?> total)
                </h5>
                <div>
                    <a href="submit_request.php" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-circle me-2"></i>
                        New Request
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($requests)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <h4 class="text-muted mt-3">No requests found</h4>
                        <p class="text-muted">Try adjusting your search criteria.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Request #</th>
                                    <th>Title</th>
                                    <th>Requester</th>
                                    <th>Department</th>
                                    <th>Category</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($requests as $req): ?>
                                <tr>
                                    <td>
                                        <span class="request-number"><?php echo htmlspecialchars($req['request_number']); ?></span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($req['title']); ?>">
                                            <?php echo htmlspecialchars($req['title']); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <?php echo htmlspecialchars($req['first_name'] . ' ' . $req['last_name']); ?>
                                        </div>
                                        <small class="text-muted"><?php echo htmlspecialchars($req['email']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($req['department_name']); ?></td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($req['category_name']); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo get_priority_badge_class($req['priority']); ?>">
                                            <?php echo PRIORITY_LEVELS[$req['priority']]; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo get_status_badge_class($req['status']); ?>">
                                            <?php echo REQUEST_STATUSES[$req['status']]; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo format_date($req['created_at'], 'M j, Y'); ?><br>
                                            <?php echo format_date($req['created_at'], 'g:i A'); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="view_request.php?id=<?php echo $req['id']; ?>" 
                                               class="btn btn-outline-primary" 
                                               title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <?php if (has_role('admin')): ?>
                                            <a href="edit_request.php?id=<?php echo $req['id']; ?>" 
                                               class="btn btn-outline-secondary" 
                                               title="Edit Request">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                    <nav aria-label="Requests pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
