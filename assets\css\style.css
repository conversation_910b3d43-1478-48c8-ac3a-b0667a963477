/* IT Request Form System - Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Status badges */
.status-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

.priority-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

/* Request cards */
.request-card {
    transition: transform 0.2s ease-in-out;
}

.request-card:hover {
    transform: translateY(-2px);
}

.request-number {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--primary-color);
}

/* Timeline */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--secondary-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 0.5rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 2px solid white;
    box-shadow: 0 0 0 2px var(--primary-color);
}

.timeline-item.completed::before {
    background-color: var(--success-color);
    box-shadow: 0 0 0 2px var(--success-color);
}

.timeline-item.rejected::before {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 2px var(--danger-color);
}

/* Forms */
.form-label {
    font-weight: 600;
    color: var(--dark-color);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.required::after {
    content: ' *';
    color: var(--danger-color);
}

/* File upload */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.15s ease-in-out;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
}

.file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

/* Tables */
.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: var(--light-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Dashboard stats */
.stat-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stat-card.success {
    background: linear-gradient(135deg, var(--success-color), #146c43);
}

.stat-card.warning {
    background: linear-gradient(135deg, var(--warning-color), #e6a700);
}

.stat-card.danger {
    background: linear-gradient(135deg, var(--danger-color), #b02a37);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* Comments */
.comment-item {
    border-left: 3px solid var(--primary-color);
    padding-left: 1rem;
    margin-bottom: 1rem;
}

.comment-item.internal {
    border-left-color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.1);
}

.comment-meta {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .timeline {
        padding-left: 1.5rem;
    }
    
    .timeline::before {
        left: 0.5rem;
    }
    
    .timeline-item::before {
        left: -1.5rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}

/* Loading spinner */
.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Utility classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

.border-start-primary {
    border-left: 3px solid var(--primary-color) !important;
}

.border-start-success {
    border-left: 3px solid var(--success-color) !important;
}

.border-start-warning {
    border-left: 3px solid var(--warning-color) !important;
}

.border-start-danger {
    border-left: 3px solid var(--danger-color) !important;
}
