<?php
require_once 'config/config.php';
require_once 'classes/Request.php';

// Require login
require_login();

$page_title = 'My Requests';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * RECORDS_PER_PAGE;

// Get user's requests
$request = new Request($db);
$requests = $request->getByUser($_SESSION['user_id'], RECORDS_PER_PAGE, $offset);
$total_requests = $request->getCount(['user_id' => $_SESSION['user_id']]);
$total_pages = ceil($total_requests / RECORDS_PER_PAGE);

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-list-ul me-2"></i>
                My Requests
            </h1>
            <a href="submit_request.php" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>
                New Request
            </a>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <?php
    $status_counts = [];
    foreach ($requests as $req) {
        $status_counts[$req['status']] = ($status_counts[$req['status']] ?? 0) + 1;
    }
    ?>
    
    <div class="col-md-3 mb-3">
        <div class="card border-start-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-muted mb-1">Total Requests</h6>
                        <h4 class="mb-0"><?php echo $total_requests; ?></h4>
                    </div>
                    <div class="text-primary">
                        <i class="bi bi-folder fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-start-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-muted mb-1">Pending</h6>
                        <h4 class="mb-0"><?php echo $status_counts['pending'] ?? 0; ?></h4>
                    </div>
                    <div class="text-warning">
                        <i class="bi bi-clock fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-start-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-muted mb-1">Completed</h6>
                        <h4 class="mb-0"><?php echo $status_counts['completed'] ?? 0; ?></h4>
                    </div>
                    <div class="text-success">
                        <i class="bi bi-check-circle fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-start-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-muted mb-1">In Progress</h6>
                        <h4 class="mb-0"><?php echo ($status_counts['in_progress'] ?? 0) + ($status_counts['dept_approved'] ?? 0) + ($status_counts['it_approved'] ?? 0); ?></h4>
                    </div>
                    <div class="text-info">
                        <i class="bi bi-gear fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Requests Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-table me-2"></i>
                    Request History
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($requests)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <h4 class="text-muted mt-3">No requests found</h4>
                        <p class="text-muted">You haven't submitted any requests yet.</p>
                        <a href="submit_request.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            Submit Your First Request
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Request #</th>
                                    <th>Title</th>
                                    <th>Category</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($requests as $req): ?>
                                <tr>
                                    <td>
                                        <span class="request-number"><?php echo htmlspecialchars($req['request_number']); ?></span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 250px;" title="<?php echo htmlspecialchars($req['title']); ?>">
                                            <?php echo htmlspecialchars($req['title']); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($req['category_name']); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo get_priority_badge_class($req['priority']); ?>">
                                            <?php echo PRIORITY_LEVELS[$req['priority']]; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo get_status_badge_class($req['status']); ?>">
                                            <?php echo REQUEST_STATUSES[$req['status']]; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo format_date($req['created_at'], 'M j, Y'); ?><br>
                                            <?php echo format_date($req['created_at'], 'g:i A'); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="view_request.php?id=<?php echo $req['id']; ?>" 
                                               class="btn btn-outline-primary" 
                                               title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <?php if ($req['status'] == 'pending'): ?>
                                            <a href="edit_request.php?id=<?php echo $req['id']; ?>" 
                                               class="btn btn-outline-secondary" 
                                               title="Edit Request">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                    <nav aria-label="Requests pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page - 1; ?>">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page + 1; ?>">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
