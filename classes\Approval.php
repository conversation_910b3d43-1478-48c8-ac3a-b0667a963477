<?php

/**
 * Approval Class
 * Handles request approval workflow
 */

class Approval
{
    private $conn;
    private $table_name = "request_approvals";

    public $id;
    public $request_id;
    public $approver_id;
    public $approval_type;
    public $status;
    public $comments;
    public $approved_at;
    public $created_at;
    public $updated_at;

    public function __construct($db)
    {
        $this->conn = $db;
    }

    // Create approval record
    public function create()
    {
        $query = "INSERT INTO " . $this->table_name . "
                  SET request_id = :request_id, approver_id = :approver_id, 
                      approval_type = :approval_type, status = :status, comments = :comments";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->comments = htmlspecialchars(strip_tags($this->comments));

        // Bind values
        $stmt->bindValue(':request_id', $this->request_id);
        $stmt->bindValue(':approver_id', $this->approver_id);
        $stmt->bindValue(':approval_type', $this->approval_type);
        $stmt->bindValue(':status', $this->status);
        $stmt->bindValue(':comments', $this->comments);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Update approval status
    public function updateStatus($new_status, $comments = '')
    {
        $query = "UPDATE " . $this->table_name . "
                  SET status = :status, comments = :comments, 
                      approved_at = " . ($new_status == 'approved' ? 'CURRENT_TIMESTAMP' : 'NULL') . ",
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $comments = htmlspecialchars(strip_tags($comments));

        // Bind values
        $stmt->bindParam(':status', $new_status);
        $stmt->bindParam(':comments', $comments);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Get approval by request and type
    public function getByRequestAndType($request_id, $approval_type)
    {
        $query = "SELECT a.*, u.first_name, u.last_name, u.email
                  FROM " . $this->table_name . " a
                  JOIN users u ON a.approver_id = u.id
                  WHERE a.request_id = :request_id AND a.approval_type = :approval_type";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':request_id', $request_id);
        $stmt->bindParam(':approval_type', $approval_type);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }

        return false;
    }

    // Get all approvals for a request
    public function getByRequest($request_id)
    {
        $query = "SELECT a.*, u.first_name, u.last_name, u.email
                  FROM " . $this->table_name . " a
                  JOIN users u ON a.approver_id = u.id
                  WHERE a.request_id = :request_id
                  ORDER BY a.created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':request_id', $request_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Process department head approval
    public function processDepartmentApproval($request_id, $approver_id, $action, $comments = '')
    {
        try {
            $this->conn->beginTransaction();

            // Check if approval already exists
            $existing = $this->getByRequestAndType($request_id, 'department_head');

            if ($existing) {
                // Update existing approval
                $this->id = $existing['id'];
                $this->updateStatus($action, $comments);
            } else {
                // Create new approval
                $this->request_id = $request_id;
                $this->approver_id = $approver_id;
                $this->approval_type = 'department_head';
                $this->status = $action;
                $this->comments = $comments;
                $this->create();
            }

            // Update request status
            $request = new Request($this->conn);
            $request->id = $request_id;

            if ($action == 'approved') {
                $request->updateStatus('dept_approved', $approver_id, 'Approved by department head: ' . $comments);

                // Create IT approval record
                $it_leads = $this->getITLeads();
                if (!empty($it_leads)) {
                    $it_lead = $it_leads[0]; // Use first IT lead
                    $it_approval = new Approval($this->conn);
                    $it_approval->request_id = $request_id;
                    $it_approval->approver_id = $it_lead['id'];
                    $it_approval->approval_type = 'it_lead';
                    $it_approval->status = 'pending';
                    $it_approval->comments = '';
                    $it_approval->create();
                }
            } else {
                $request->updateStatus('rejected', $approver_id, 'Rejected by department head: ' . $comments);
            }

            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }

    // Process IT lead approval
    public function processITApproval($request_id, $approver_id, $action, $comments = '')
    {
        try {
            $this->conn->beginTransaction();

            // Check if approval already exists
            $existing = $this->getByRequestAndType($request_id, 'it_lead');

            if ($existing) {
                // Update existing approval
                $this->id = $existing['id'];
                $this->updateStatus($action, $comments);
            } else {
                // Create new approval
                $this->request_id = $request_id;
                $this->approver_id = $approver_id;
                $this->approval_type = 'it_lead';
                $this->status = $action;
                $this->comments = $comments;
                $this->create();
            }

            // Update request status
            $request = new Request($this->conn);
            $request->id = $request_id;

            if ($action == 'approved') {
                $request->updateStatus('it_approved', $approver_id, 'Approved by IT lead: ' . $comments);
            } else {
                $request->updateStatus('rejected', $approver_id, 'Rejected by IT lead: ' . $comments);
            }

            $this->conn->commit();
            return true;
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }

    // Get IT leads
    private function getITLeads()
    {
        $query = "SELECT * FROM users WHERE role = 'it_lead' AND is_active = 1 ORDER BY first_name, last_name";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Check if user can approve request
    public function canUserApprove($user_id, $user_role, $request_id)
    {
        // Get request details
        $query = "SELECT r.*, u.department_id FROM it_requests r 
                  JOIN users u ON r.user_id = u.id 
                  WHERE r.id = :request_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':request_id', $request_id);
        $stmt->execute();
        $request = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$request) return false;

        // Check based on role and request status
        if ($user_role == 'department_head') {
            // Department head can approve if request is pending and from their department
            if ($request['status'] == 'pending') {
                $query = "SELECT department_id FROM users WHERE id = :user_id";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
                $user_dept = $stmt->fetch(PDO::FETCH_ASSOC);

                return $user_dept && $user_dept['department_id'] == $request['department_id'];
            }
        } elseif ($user_role == 'it_lead' || $user_role == 'admin') {
            // IT lead can approve if request is department approved
            return $request['status'] == 'dept_approved';
        }

        return false;
    }

    // Get pending approvals for user
    public function getPendingApprovalsForUser($user_id, $user_role)
    {
        if ($user_role == 'department_head') {
            // Get requests from user's department that need approval
            $query = "SELECT r.*, u.first_name, u.last_name, c.name as category_name
                      FROM it_requests r
                      JOIN users u ON r.user_id = u.id
                      JOIN request_categories c ON r.category_id = c.id
                      WHERE r.status = 'pending' 
                      AND u.department_id = (SELECT department_id FROM users WHERE id = :user_id)
                      ORDER BY r.created_at ASC";
        } elseif ($user_role == 'it_lead' || $user_role == 'admin') {
            // Get requests that need IT approval
            $query = "SELECT r.*, u.first_name, u.last_name, c.name as category_name, d.name as department_name
                      FROM it_requests r
                      JOIN users u ON r.user_id = u.id
                      JOIN request_categories c ON r.category_id = c.id
                      LEFT JOIN departments d ON u.department_id = d.id
                      WHERE r.status = 'dept_approved'
                      ORDER BY r.created_at ASC";
        } else {
            return [];
        }

        $stmt = $this->conn->prepare($query);
        if ($user_role == 'department_head') {
            $stmt->bindParam(':user_id', $user_id);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get approval statistics
    public function getApprovalStats($user_id, $user_role)
    {
        $stats = [];

        if ($user_role == 'department_head') {
            // Count pending department approvals
            $query = "SELECT COUNT(*) as count FROM it_requests r
                      JOIN users u ON r.user_id = u.id
                      WHERE r.status = 'pending' 
                      AND u.department_id = (SELECT department_id FROM users WHERE id = :user_id)";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            $stats['pending'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        } elseif ($user_role == 'it_lead' || $user_role == 'admin') {
            // Count pending IT approvals
            $query = "SELECT COUNT(*) as count FROM it_requests WHERE status = 'dept_approved'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $stats['pending'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        }

        return $stats;
    }
}
