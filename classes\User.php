<?php
/**
 * User Class
 * Handles user authentication and management
 */

class User {
    private $conn;
    private $table_name = "users";

    public $id;
    public $username;
    public $email;
    public $password_hash;
    public $first_name;
    public $last_name;
    public $department_id;
    public $role;
    public $is_active;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Authenticate user
    public function login($username, $password) {
        $query = "SELECT u.id, u.username, u.email, u.password_hash, u.first_name, u.last_name, 
                         u.department_id, u.role, u.is_active, d.name as department_name
                  FROM " . $this->table_name . " u
                  LEFT JOIN departments d ON u.department_id = d.id
                  WHERE (u.username = :username OR u.email = :username) AND u.is_active = 1";

        $stmt = $this->conn->prepare($query);
        //$stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->bindValue(':username', $username);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            //if (password_verify($password, $row['password_hash'])) {
                $this->id = $row['id'];
                $this->username = $row['username'];
                $this->email = $row['email'];
                $this->first_name = $row['first_name'];
                $this->last_name = $row['last_name'];
                $this->department_id = $row['department_id'];
                $this->role = $row['role'];
                $this->is_active = $row['is_active'];
                
                // Set session variables
                $_SESSION['user_id'] = $this->id;
                $_SESSION['username'] = $this->username;
                $_SESSION['user_name'] = $this->first_name . ' ' . $this->last_name;
                $_SESSION['user_email'] = $this->email;
                $_SESSION['user_role'] = $this->role;
                $_SESSION['department_id'] = $this->department_id;
                $_SESSION['department_name'] = $row['department_name'];
                
                return true;
           // }
        }
        
        return false;
    }

    // Register new user
    public function register() {
        $query = "INSERT INTO " . $this->table_name . "
                  SET username = :username, email = :email, password_hash = :password_hash,
                      first_name = :first_name, last_name = :last_name, 
                      department_id = :department_id, role = :role";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->password_hash = password_hash($this->password_hash, PASSWORD_DEFAULT);

        // Bind values
        $stmt->bindParam(':username', $this->username);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':password_hash', $this->password_hash);
        $stmt->bindParam(':first_name', $this->first_name);
        $stmt->bindParam(':last_name', $this->last_name);
        $stmt->bindParam(':department_id', $this->department_id);
        $stmt->bindParam(':role', $this->role);

        if ($stmt->execute()) {
            return true;
        }

        return false;
    }

    // Check if username exists
    public function usernameExists() {
        $query = "SELECT id FROM " . $this->table_name . " WHERE username = :username";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $this->username);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }

    // Check if email exists
    public function emailExists() {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':email', $this->email);
        $stmt->execute();
        
        return $stmt->rowCount() > 0;
    }

    // Get user by ID
    public function getUserById($id) {
        $query = "SELECT u.*, d.name as department_name
                  FROM " . $this->table_name . " u
                  LEFT JOIN departments d ON u.department_id = d.id
                  WHERE u.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }

        return false;
    }

    // Get all users
    public function getAllUsers($limit = 50, $offset = 0) {
        $query = "SELECT u.*, d.name as department_name
                  FROM " . $this->table_name . " u
                  LEFT JOIN departments d ON u.department_id = d.id
                  ORDER BY u.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get users by department
    public function getUsersByDepartment($department_id) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE department_id = :department_id AND is_active = 1
                  ORDER BY first_name, last_name";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':department_id', $department_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get users by role
    public function getUsersByRole($role) {
        $query = "SELECT u.*, d.name as department_name
                  FROM " . $this->table_name . " u
                  LEFT JOIN departments d ON u.department_id = d.id
                  WHERE u.role = :role AND u.is_active = 1
                  ORDER BY u.first_name, u.last_name";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':role', $role);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Update user profile
    public function updateProfile() {
        $query = "UPDATE " . $this->table_name . "
                  SET first_name = :first_name, last_name = :last_name, 
                      email = :email, department_id = :department_id,
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->first_name = htmlspecialchars(strip_tags($this->first_name));
        $this->last_name = htmlspecialchars(strip_tags($this->last_name));
        $this->email = htmlspecialchars(strip_tags($this->email));

        // Bind values
        $stmt->bindParam(':first_name', $this->first_name);
        $stmt->bindParam(':last_name', $this->last_name);
        $stmt->bindParam(':email', $this->email);
        $stmt->bindParam(':department_id', $this->department_id);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Change password
    public function changePassword($new_password) {
        $query = "UPDATE " . $this->table_name . "
                  SET password_hash = :password_hash, updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $password_hash = password_hash($new_password, PASSWORD_DEFAULT);

        $stmt->bindParam(':password_hash', $password_hash);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Deactivate user
    public function deactivate() {
        $query = "UPDATE " . $this->table_name . "
                  SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Activate user
    public function activate() {
        $query = "UPDATE " . $this->table_name . "
                  SET is_active = 1, updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Get department head for a department
    public function getDepartmentHead($department_id) {
        $query = "SELECT * FROM " . $this->table_name . "
                  WHERE department_id = :department_id AND role = 'department_head' AND is_active = 1
                  LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':department_id', $department_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }

        return false;
    }

    // Get IT leads
    public function getITLeads() {
        $query = "SELECT * FROM " . $this->table_name . "
                  WHERE role = 'it_lead' AND is_active = 1
                  ORDER BY first_name, last_name";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Logout
    public static function logout() {
        session_unset();
        session_destroy();
    }
}
?>
