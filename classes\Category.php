<?php
/**
 * Category Class
 * Handles request categories management
 */

class Category {
    private $conn;
    private $table_name = "request_categories";

    public $id;
    public $name;
    public $type;
    public $description;
    public $is_active;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Get all active categories
    public function getActive() {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE is_active = 1 
                  ORDER BY type, name";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get categories by type
    public function getByType($type) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE type = :type AND is_active = 1 
                  ORDER BY name";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':type', $type);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get category by ID
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }

        return false;
    }

    // Create new category
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  SET name = :name, type = :type, description = :description";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->type = htmlspecialchars(strip_tags($this->type));
        $this->description = htmlspecialchars(strip_tags($this->description));

        // Bind values
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':type', $this->type);
        $stmt->bindParam(':description', $this->description);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Update category
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET name = :name, type = :type, description = :description,
                      updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->type = htmlspecialchars(strip_tags($this->type));
        $this->description = htmlspecialchars(strip_tags($this->description));

        // Bind values
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':type', $this->type);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Toggle active status
    public function toggleActive() {
        $query = "UPDATE " . $this->table_name . "
                  SET is_active = NOT is_active, updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Delete category
    public function delete() {
        // Check if category is being used
        $query = "SELECT COUNT(*) as count FROM it_requests WHERE category_id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        $stmt->execute();
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result['count'] > 0) {
            return false; // Cannot delete category that's being used
        }

        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Get all categories (including inactive)
    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY type, name";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get category statistics
    public function getStatistics() {
        $query = "SELECT c.id, c.name, c.type, COUNT(r.id) as request_count
                  FROM " . $this->table_name . " c
                  LEFT JOIN it_requests r ON c.id = r.category_id
                  WHERE c.is_active = 1
                  GROUP BY c.id, c.name, c.type
                  ORDER BY request_count DESC, c.name";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
