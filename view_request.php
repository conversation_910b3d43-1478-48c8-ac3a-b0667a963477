<?php
require_once 'config/config.php';
require_once 'classes/Request.php';
require_once 'classes/Approval.php';

// Require login
require_login();

$page_title = 'View Request';
$error_message = '';
$success_message = '';

// Get request ID
$request_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (empty($request_id)) {
    redirect('dashboard.php');
}

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Get request details
$request_obj = new Request($db);
$request = $request_obj->getById($request_id);

if (!$request) {
    handle_error('Request not found.', 'dashboard.php');
}

// Check if user can view this request
$can_view = false;
if ($request['user_id'] == $_SESSION['user_id']) {
    $can_view = true; // Owner can always view
} elseif (has_role(['admin', 'it_lead', 'it_helpdesk'])) {
    $can_view = true; // Admin, IT lead, and IT helpdesk can view all
} elseif (has_role('department_head') && $request['department_id'] == $_SESSION['department_id']) {
    $can_view = true; // Department head can view requests from their department
}

if (!$can_view) {
    handle_error('You are not authorized to view this request.', 'dashboard.php');
}

// Get status history
$status_history = $request_obj->getStatusHistory($request_id);

// Get approvals
$approval_obj = new Approval($db);
$approvals = $approval_obj->getByRequest($request_id);

// Get attachments
$query = "SELECT * FROM request_attachments WHERE request_id = :request_id ORDER BY created_at ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':request_id', $request_id);
$stmt->execute();
$attachments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get comments
$query = "SELECT c.*, u.first_name, u.last_name FROM request_comments c
          JOIN users u ON c.user_id = u.id
          WHERE c.request_id = :request_id
          ORDER BY c.created_at ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':request_id', $request_id);
$stmt->execute();
$comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle status updates for IT helpdesk
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_status'])) {
    $action = $_POST['action'];
    $status_comments = sanitize_input($_POST['status_comments'] ?? '');

    if (in_array($action, ['start_work', 'complete_work']) && has_role(['it_helpdesk', 'it_lead', 'admin'])) {
        $new_status = '';
        $status_comment = '';

        if ($action == 'start_work' && $request['status'] == 'it_approved') {
            $new_status = 'in_progress';
            $status_comment = 'Work started by ' . $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];
            if (!empty($status_comments)) {
                $status_comment .= ': ' . $status_comments;
            }
        } elseif ($action == 'complete_work' && $request['status'] == 'in_progress') {
            $new_status = 'completed';
            $status_comment = 'Work completed by ' . $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];
            if (!empty($status_comments)) {
                $status_comment .= ': ' . $status_comments;
            }

            // Set actual completion date
            $query = "UPDATE it_requests SET actual_completion_date = CURRENT_DATE WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $request_id);
            $stmt->execute();
        }

        if (!empty($new_status)) {
            if ($request_obj->updateStatus($new_status, $_SESSION['user_id'], $status_comment)) {
                $success_message = 'Request status updated successfully.';
                // Refresh request data
                $request = $request_obj->getById($request_id);
                // Refresh status history
                $status_history = $request_obj->getStatusHistory($request_id);
            } else {
                $error_message = 'Failed to update request status.';
            }
        } else {
            $error_message = 'Invalid status transition.';
        }
    } else {
        $error_message = 'You are not authorized to update this request status.';
    }
}

// Handle new comment submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_comment'])) {
    $comment_text = sanitize_input($_POST['comment']);
    $is_internal = isset($_POST['is_internal']) ? 1 : 0;

    if (!empty($comment_text)) {
        $query = "INSERT INTO request_comments
                  SET request_id = :request_id, user_id = :user_id, comment = :comment, is_internal = :is_internal";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':request_id', $request_id);
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->bindParam(':comment', $comment_text);
        $stmt->bindParam(':is_internal', $is_internal);

        if ($stmt->execute()) {
            $success_message = 'Comment added successfully.';
            // Refresh comments
            $stmt = $db->prepare("SELECT c.*, u.first_name, u.last_name FROM request_comments c
                                  JOIN users u ON c.user_id = u.id
                                  WHERE c.request_id = :request_id
                                  ORDER BY c.created_at ASC");
            $stmt->bindParam(':request_id', $request_id);
            $stmt->execute();
            $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            $error_message = 'Failed to add comment.';
        }
    }
}

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-eye me-2"></i>
                Request Details
            </h1>
            <div>
                <?php if ($request['user_id'] == $_SESSION['user_id']): ?>
                <a href="my_requests.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to My Requests
                </a>
                <?php elseif (has_role(['admin', 'it_lead'])): ?>
                <a href="all_requests.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to All Requests
                </a>
                <?php elseif (has_role('it_helpdesk')): ?>
                <a href="it_work_queue.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to Work Queue
                </a>
                <?php else: ?>
                <a href="dashboard.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to Dashboard
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Request Details -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="request-number me-2"><?php echo htmlspecialchars($request['request_number']); ?></span>
                        <?php echo htmlspecialchars($request['title']); ?>
                    </h5>
                    <span class="badge bg-<?php echo get_status_badge_class($request['status']); ?> fs-6">
                        <?php echo REQUEST_STATUSES[$request['status']]; ?>
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Submitted by:</strong> <?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?></p>
                        <p class="mb-1"><strong>Department:</strong> <?php echo htmlspecialchars($request['department_name']); ?></p>
                        <p class="mb-1"><strong>Email:</strong> <?php echo htmlspecialchars($request['email']); ?></p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Category:</strong> 
                            <span class="badge bg-secondary"><?php echo htmlspecialchars($request['category_name']); ?></span>
                        </p>
                        <p class="mb-1"><strong>Priority:</strong> 
                            <span class="badge bg-<?php echo get_priority_badge_class($request['priority']); ?>">
                                <?php echo PRIORITY_LEVELS[$request['priority']]; ?>
                            </span>
                        </p>
                        <p class="mb-1"><strong>Submitted:</strong> <?php echo format_date($request['created_at'], 'M j, Y g:i A'); ?></p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <?php if (!empty($request['requested_completion_date'])): ?>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Requested Completion:</strong> <?php echo format_date($request['requested_completion_date'], 'M j, Y'); ?></p>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($request['estimated_cost'])): ?>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Estimated Cost:</strong> $<?php echo number_format($request['estimated_cost'], 2); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
                
                <hr>
                
                <h6>Description:</h6>
                <p class="text-muted"><?php echo nl2br(htmlspecialchars($request['description'])); ?></p>
                
                <?php if (!empty($attachments)): ?>
                <hr>
                <h6>Attachments:</h6>
                <div class="list-group">
                    <?php foreach ($attachments as $attachment): ?>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-file-earmark me-2"></i>
                            <?php echo htmlspecialchars($attachment['original_filename']); ?>
                            <small class="text-muted">(<?php echo number_format($attachment['file_size'] / 1024, 1); ?> KB)</small>
                        </div>
                        <a href="download.php?id=<?php echo $attachment['id']; ?>" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-download"></i>
                        </a>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Status Update Controls for IT Helpdesk -->
        <?php if (has_role(['it_helpdesk', 'it_lead', 'admin']) && in_array($request['status'], ['it_approved', 'in_progress'])): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>
                    Status Update
                </h5>
            </div>
            <div class="card-body">
                <?php if ($request['status'] == 'it_approved'): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        This request is ready to start. Click "Start Work" to begin working on it.
                    </div>

                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="status_comments" class="form-label">Work Notes (Optional)</label>
                            <textarea class="form-control" id="status_comments" name="status_comments" rows="3"
                                      placeholder="Add any notes about starting work on this request..."></textarea>
                        </div>

                        <button type="submit" name="update_status" value="1"
                                onclick="return confirm('Start working on this request?')"
                                class="btn btn-primary">
                            <input type="hidden" name="action" value="start_work">
                            <i class="bi bi-play-fill me-2"></i>
                            Start Work
                        </button>
                    </form>

                <?php elseif ($request['status'] == 'in_progress'): ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-gear me-2"></i>
                        This request is currently in progress. Click "Complete Work" when finished.
                    </div>

                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="status_comments" class="form-label">Completion Notes (Optional)</label>
                            <textarea class="form-control" id="status_comments" name="status_comments" rows="3"
                                      placeholder="Add any notes about the completed work..."></textarea>
                        </div>

                        <button type="submit" name="update_status" value="1"
                                onclick="return confirm('Mark this request as completed?')"
                                class="btn btn-success">
                            <input type="hidden" name="action" value="complete_work">
                            <i class="bi bi-check-lg me-2"></i>
                            Complete Work
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Comments Section -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-chat-dots me-2"></i>
                    Comments
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Existing Comments -->
                <?php if (!empty($comments)): ?>
                    <?php foreach ($comments as $comment): ?>
                        <?php if (!$comment['is_internal'] || has_role(['admin', 'it_lead', 'it_helpdesk', 'department_head'])): ?>
                        <div class="comment-item <?php echo $comment['is_internal'] ? 'internal' : ''; ?>">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <strong><?php echo htmlspecialchars($comment['first_name'] . ' ' . $comment['last_name']); ?></strong>
                                    <?php if ($comment['is_internal']): ?>
                                        <span class="badge bg-warning ms-2">Internal</span>
                                    <?php endif; ?>
                                </div>
                                <small class="text-muted"><?php echo format_date($comment['created_at'], 'M j, Y g:i A'); ?></small>
                            </div>
                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($comment['comment'])); ?></p>
                        </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                    <hr>
                <?php endif; ?>
                
                <!-- Add Comment Form -->
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="comment" class="form-label">Add Comment</label>
                        <textarea class="form-control" id="comment" name="comment" rows="3" required></textarea>
                    </div>
                    
                    <?php if (has_role(['admin', 'it_lead', 'it_helpdesk', 'department_head'])): ?>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_internal" name="is_internal">
                            <label class="form-check-label" for="is_internal">
                                Internal comment (visible only to staff)
                            </label>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <button type="submit" name="add_comment" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add Comment
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Status Timeline -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    Status Timeline
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <?php foreach ($status_history as $history): ?>
                    <div class="timeline-item <?php echo in_array($history['new_status'], ['completed', 'it_approved', 'dept_approved']) ? 'completed' : (in_array($history['new_status'], ['rejected', 'cancelled']) ? 'rejected' : ''); ?>">
                        <div class="d-flex justify-content-between align-items-start mb-1">
                            <strong><?php echo REQUEST_STATUSES[$history['new_status']]; ?></strong>
                            <small class="text-muted"><?php echo format_date($history['created_at'], 'M j'); ?></small>
                        </div>
                        <p class="mb-1 text-muted small">
                            by <?php echo htmlspecialchars($history['first_name'] . ' ' . $history['last_name']); ?>
                        </p>
                        <?php if (!empty($history['comments'])): ?>
                        <p class="mb-0 small"><?php echo htmlspecialchars($history['comments']); ?></p>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- Approval Status -->
        <?php if (!empty($approvals)): ?>
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-check-circle me-2"></i>
                    Approval Status
                </h6>
            </div>
            <div class="card-body">
                <?php foreach ($approvals as $approval): ?>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <strong><?php echo ucfirst(str_replace('_', ' ', $approval['approval_type'])); ?></strong>
                        <span class="badge bg-<?php echo $approval['status'] == 'approved' ? 'success' : ($approval['status'] == 'rejected' ? 'danger' : 'warning'); ?>">
                            <?php echo ucfirst($approval['status']); ?>
                        </span>
                    </div>
                    <p class="mb-1 small text-muted">
                        <?php echo htmlspecialchars($approval['first_name'] . ' ' . $approval['last_name']); ?>
                    </p>
                    <?php if ($approval['status'] != 'pending'): ?>
                    <p class="mb-1 small text-muted">
                        <?php echo format_date($approval['approved_at'], 'M j, Y g:i A'); ?>
                    </p>
                    <?php endif; ?>
                    <?php if (!empty($approval['comments'])): ?>
                    <p class="mb-0 small"><?php echo htmlspecialchars($approval['comments']); ?></p>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
