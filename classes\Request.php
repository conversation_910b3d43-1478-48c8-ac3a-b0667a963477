<?php
/**
 * Request Class
 * Handles IT request management
 */

class Request {
    private $conn;
    private $table_name = "it_requests";

    public $id;
    public $request_number;
    public $user_id;
    public $category_id;
    public $title;
    public $description;
    public $priority;
    public $status;
    public $requested_completion_date;
    public $actual_completion_date;
    public $estimated_cost;
    public $actual_cost;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Create new request
    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                  SET request_number = :request_number, user_id = :user_id, category_id = :category_id,
                      title = :title, description = :description, priority = :priority,
                      requested_completion_date = :requested_completion_date, estimated_cost = :estimated_cost";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->request_number = htmlspecialchars(strip_tags($this->request_number));
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->description = htmlspecialchars(strip_tags($this->description));

        // Bind values
        $stmt->bindParam(':request_number', $this->request_number);
        $stmt->bindParam(':user_id', $this->user_id);
        $stmt->bindParam(':category_id', $this->category_id);
        $stmt->bindParam(':title', $this->title);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':priority', $this->priority);
        $stmt->bindParam(':requested_completion_date', $this->requested_completion_date);
        $stmt->bindParam(':estimated_cost', $this->estimated_cost);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Get request by ID
    public function getById($id) {
        $query = "SELECT r.*, u.first_name, u.last_name, u.email, u.department_id,
                         c.name as category_name, c.type as category_type,
                         d.name as department_name
                  FROM " . $this->table_name . " r
                  JOIN users u ON r.user_id = u.id
                  JOIN request_categories c ON r.category_id = c.id
                  LEFT JOIN departments d ON u.department_id = d.id
                  WHERE r.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }

        return false;
    }

    // Get requests by user
    public function getByUser($user_id, $limit = 50, $offset = 0) {
        $query = "SELECT r.*, c.name as category_name, c.type as category_type
                  FROM " . $this->table_name . " r
                  JOIN request_categories c ON r.category_id = c.id
                  WHERE r.user_id = :user_id
                  ORDER BY r.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get all requests
    public function getAll($limit = 50, $offset = 0, $filters = []) {
        $where_conditions = [];
        $params = [];

        // Build WHERE clause based on filters
        if (!empty($filters['status'])) {
            $where_conditions[] = "r.status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($filters['priority'])) {
            $where_conditions[] = "r.priority = :priority";
            $params[':priority'] = $filters['priority'];
        }

        if (!empty($filters['category_id'])) {
            $where_conditions[] = "r.category_id = :category_id";
            $params[':category_id'] = $filters['category_id'];
        }

        if (!empty($filters['department_id'])) {
            $where_conditions[] = "u.department_id = :department_id";
            $params[':department_id'] = $filters['department_id'];
        }

        if (!empty($filters['search'])) {
            $where_conditions[] = "(r.title LIKE :search OR r.description LIKE :search OR r.request_number LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

        $query = "SELECT r.*, u.first_name, u.last_name, u.email,
                         c.name as category_name, c.type as category_type,
                         d.name as department_name
                  FROM " . $this->table_name . " r
                  JOIN users u ON r.user_id = u.id
                  JOIN request_categories c ON r.category_id = c.id
                  LEFT JOIN departments d ON u.department_id = d.id
                  $where_clause
                  ORDER BY r.created_at DESC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        
        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get requests needing department approval
    public function getPendingDepartmentApprovals($department_id) {
        $query = "SELECT r.*, u.first_name, u.last_name, u.email,
                         c.name as category_name, c.type as category_type
                  FROM " . $this->table_name . " r
                  JOIN users u ON r.user_id = u.id
                  JOIN request_categories c ON r.category_id = c.id
                  WHERE u.department_id = :department_id AND r.status = 'pending'
                  ORDER BY r.created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':department_id', $department_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get requests needing IT approval
    public function getPendingITApprovals() {
        $query = "SELECT r.*, u.first_name, u.last_name, u.email,
                         c.name as category_name, c.type as category_type,
                         d.name as department_name
                  FROM " . $this->table_name . " r
                  JOIN users u ON r.user_id = u.id
                  JOIN request_categories c ON r.category_id = c.id
                  LEFT JOIN departments d ON u.department_id = d.id
                  WHERE r.status = 'dept_approved'
                  ORDER BY r.created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get requests ready for IT work (IT approved)
    public function getITWorkQueue() {
        $query = "SELECT r.*, u.first_name, u.last_name, u.email,
                         c.name as category_name, c.type as category_type,
                         d.name as department_name
                  FROM " . $this->table_name . " r
                  JOIN users u ON r.user_id = u.id
                  JOIN request_categories c ON r.category_id = c.id
                  LEFT JOIN departments d ON u.department_id = d.id
                  WHERE r.status = 'it_approved'
                  ORDER BY r.priority DESC, r.created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get requests currently in progress
    public function getInProgressRequests() {
        $query = "SELECT r.*, u.first_name, u.last_name, u.email,
                         c.name as category_name, c.type as category_type,
                         d.name as department_name
                  FROM " . $this->table_name . " r
                  JOIN users u ON r.user_id = u.id
                  JOIN request_categories c ON r.category_id = c.id
                  LEFT JOIN departments d ON u.department_id = d.id
                  WHERE r.status = 'in_progress'
                  ORDER BY r.priority DESC, r.created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Check if user can update request status
    public function canUserUpdateStatus($user_role, $request_id, $new_status) {
        // Get request details
        $request = $this->getById($request_id);
        if (!$request) {
            return false;
        }

        // IT helpdesk can start work on IT approved requests
        if ($user_role == 'it_helpdesk' || $user_role == 'it_lead' || $user_role == 'admin') {
            if ($new_status == 'in_progress' && $request['status'] == 'it_approved') {
                return true;
            }
            if ($new_status == 'completed' && $request['status'] == 'in_progress') {
                return true;
            }
        }

        return false;
    }

    // Update request status
    public function updateStatus($new_status, $changed_by, $comments = '') {
        // Get current status
        $query = "SELECT status FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);
        $stmt->execute();
        $current_status = $stmt->fetch(PDO::FETCH_ASSOC)['status'];

        // Update request status
        $query = "UPDATE " . $this->table_name . "
                  SET status = :status, updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $new_status);
        $stmt->bindParam(':id', $this->id);

        if ($stmt->execute()) {
            // Add to status history
            $this->addStatusHistory($current_status, $new_status, $changed_by, $comments);
            return true;
        }

        return false;
    }

    // Add status history entry
    private function addStatusHistory($old_status, $new_status, $changed_by, $comments) {
        $query = "INSERT INTO request_status_history
                  SET request_id = :request_id, old_status = :old_status, new_status = :new_status,
                      changed_by = :changed_by, comments = :comments";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':request_id', $this->id);
        $stmt->bindParam(':old_status', $old_status);
        $stmt->bindParam(':new_status', $new_status);
        $stmt->bindParam(':changed_by', $changed_by);
        $stmt->bindParam(':comments', $comments);

        return $stmt->execute();
    }

    // Get status history
    public function getStatusHistory($request_id) {
        $query = "SELECT h.*, u.first_name, u.last_name
                  FROM request_status_history h
                  JOIN users u ON h.changed_by = u.id
                  WHERE h.request_id = :request_id
                  ORDER BY h.created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':request_id', $request_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Generate unique request number
    public function generateRequestNumber() {
        do {
            $number = generate_request_number();
            $query = "SELECT id FROM " . $this->table_name . " WHERE request_number = :number";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':number', $number);
            $stmt->execute();
        } while ($stmt->rowCount() > 0);

        return $number;
    }

    // Update request
    public function update() {
        $query = "UPDATE " . $this->table_name . "
                  SET title = :title, description = :description, priority = :priority,
                      requested_completion_date = :requested_completion_date, 
                      estimated_cost = :estimated_cost, updated_at = CURRENT_TIMESTAMP
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->description = htmlspecialchars(strip_tags($this->description));

        // Bind values
        $stmt->bindParam(':title', $this->title);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':priority', $this->priority);
        $stmt->bindParam(':requested_completion_date', $this->requested_completion_date);
        $stmt->bindParam(':estimated_cost', $this->estimated_cost);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Delete request
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $this->id);

        return $stmt->execute();
    }

    // Get request count
    public function getCount($filters = []) {
        $where_conditions = [];
        $params = [];

        // Build WHERE clause based on filters
        if (!empty($filters['status'])) {
            $where_conditions[] = "r.status = :status";
            $params[':status'] = $filters['status'];
        }

        if (!empty($filters['user_id'])) {
            $where_conditions[] = "r.user_id = :user_id";
            $params[':user_id'] = $filters['user_id'];
        }

        if (!empty($filters['department_id'])) {
            $where_conditions[] = "u.department_id = :department_id";
            $params[':department_id'] = $filters['department_id'];
        }

        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

        $query = "SELECT COUNT(*) as total
                  FROM " . $this->table_name . " r
                  JOIN users u ON r.user_id = u.id
                  $where_clause";

        $stmt = $this->conn->prepare($query);
        
        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    }
}
?>
