<?php
require_once '../config/config.php';

// Require admin role
require_role('admin');

$page_title = 'Admin Dashboard';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Get statistics
$stats = [];

// Total users
$query = "SELECT COUNT(*) as total FROM users WHERE is_active = 1";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_users'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// Total requests
$query = "SELECT COUNT(*) as total FROM it_requests";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['total_requests'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// Pending requests
$query = "SELECT COUNT(*) as total FROM it_requests WHERE status IN ('pending', 'dept_approved')";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['pending_requests'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// Active categories
$query = "SELECT COUNT(*) as total FROM request_categories WHERE is_active = 1";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['active_categories'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// Recent activity
$query = "SELECT r.*, u.first_name, u.last_name, c.name as category_name
          FROM it_requests r
          JOIN users u ON r.user_id = u.id
          JOIN request_categories c ON r.category_id = c.id
          ORDER BY r.created_at DESC
          LIMIT 10";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-gear-wide-connected me-2"></i>
            Admin Dashboard
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['total_users']; ?></div>
            <div class="stat-label">Active Users</div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stat-card success">
            <div class="stat-number"><?php echo $stats['total_requests']; ?></div>
            <div class="stat-label">Total Requests</div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stat-card warning">
            <div class="stat-number"><?php echo $stats['pending_requests']; ?></div>
            <div class="stat-label">Pending Requests</div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stat-card danger">
            <div class="stat-number"><?php echo $stats['active_categories']; ?></div>
            <div class="stat-label">Active Categories</div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning-fill me-2"></i>
                    Admin Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="users.php" class="btn btn-primary w-100">
                            <i class="bi bi-people me-2"></i>
                            Manage Users
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="departments.php" class="btn btn-outline-primary w-100">
                            <i class="bi bi-building me-2"></i>
                            Manage Departments
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="categories.php" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-tags me-2"></i>
                            Manage Categories
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="reports.php" class="btn btn-outline-success w-100">
                            <i class="bi bi-graph-up me-2"></i>
                            View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    Recent Activity
                </h5>
                <a href="../all_requests.php" class="btn btn-sm btn-outline-primary">
                    View All Requests
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_requests)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <p class="text-muted mt-2">No recent activity.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Request #</th>
                                    <th>Title</th>
                                    <th>Requester</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_requests as $request): ?>
                                <tr>
                                    <td>
                                        <span class="request-number"><?php echo htmlspecialchars($request['request_number']); ?></span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;">
                                            <?php echo htmlspecialchars($request['title']); ?>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?></td>
                                    <td><?php echo htmlspecialchars($request['category_name']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo get_status_badge_class($request['status']); ?>">
                                            <?php echo REQUEST_STATUSES[$request['status']]; ?>
                                        </span>
                                    </td>
                                    <td><?php echo format_date($request['created_at'], 'M j, Y'); ?></td>
                                    <td>
                                        <a href="../view_request.php?id=<?php echo $request['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
