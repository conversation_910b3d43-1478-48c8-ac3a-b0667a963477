<?php
require_once 'config/config.php';
require_once 'classes/User.php';

// Require login
require_login();

$page_title = 'Dashboard';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Get dashboard statistics
$stats = [];

//var_dump($_SESSION);

// Total requests by user
$query = "SELECT COUNT(*) as total FROM it_requests WHERE user_id = :user_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();
$stats['my_requests'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// Pending requests by user
$query = "SELECT COUNT(*) as total FROM it_requests WHERE user_id = :user_id AND status = 'pending'";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();
$stats['my_pending'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// If department head or IT lead, get approval statistics
if (has_role(['department_head', 'it_lead'])) {
    if (has_role('department_head')) {
        // Requests needing department approval
        $query = "SELECT COUNT(*) as total FROM it_requests r 
                  JOIN users u ON r.user_id = u.id 
                  WHERE u.department_id = :dept_id AND r.status = 'pending'";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':dept_id', $_SESSION['department_id']);
        $stmt->execute();
        $stats['dept_approvals'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    }
    
    if (has_role(['it_lead', 'admin'])) {
        // Requests needing IT approval
        $query = "SELECT COUNT(*) as total FROM it_requests WHERE status = 'dept_approved'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['it_approvals'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // Total system requests
        $query = "SELECT COUNT(*) as total FROM it_requests";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $stats['total_requests'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    }
}

// Get recent requests
$recent_requests = [];
if (has_role(['it_lead', 'admin'])) {
    $query = "SELECT r.*, u.first_name, u.last_name, c.name as category_name
              FROM it_requests r
              JOIN users u ON r.user_id = u.id
              JOIN request_categories c ON r.category_id = c.id
              ORDER BY r.created_at DESC
              LIMIT 5";
} else {
    $query = "SELECT r.*, u.first_name, u.last_name, c.name as category_name
              FROM it_requests r
              JOIN users u ON r.user_id = u.id
              JOIN request_categories c ON r.category_id = c.id
              WHERE r.user_id = :user_id
              ORDER BY r.created_at DESC
              LIMIT 5";
}

$stmt = $db->prepare($query);
if (!has_role(['it_lead', 'admin'])) {
    $stmt->bindParam(':user_id', $_SESSION['user_id']);
}
$stmt->execute();
$recent_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-speedometer2 me-2"></i>
            Dashboard
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo $stats['my_requests']; ?></div>
            <div class="stat-label">My Requests</div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stat-card warning">
            <div class="stat-number"><?php echo $stats['my_pending']; ?></div>
            <div class="stat-label">Pending Requests</div>
        </div>
    </div>
    
    <?php if (has_role('department_head')): ?>
    <div class="col-md-3 mb-3">
        <div class="stat-card danger">
            <div class="stat-number"><?php echo $stats['dept_approvals']; ?></div>
            <div class="stat-label">Needs Approval</div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (has_role(['it_lead', 'admin'])): ?>
    <div class="col-md-3 mb-3">
        <div class="stat-card danger">
            <div class="stat-number"><?php echo $stats['it_approvals']??'0'; ?></div>
            <div class="stat-label">IT Approvals</div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="stat-card success">
            <div class="stat-number"><?php echo $stats['total_requests']??'0'; ?></div>
            <div class="stat-label">Total Requests</div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning-fill me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="submit_request.php" class="btn btn-primary w-100">
                            <i class="bi bi-plus-circle me-2"></i>
                            New Request
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="my_requests.php" class="btn btn-outline-primary w-100">
                            <i class="bi bi-list-ul me-2"></i>
                            My Requests
                        </a>
                    </div>
                    
                    <?php if (has_role('department_head')): ?>
                    <div class="col-md-3 mb-2">
                        <a href="department_approvals.php" class="btn btn-warning w-100">
                            <i class="bi bi-check-circle me-2"></i>
                            Approvals
                            <?php if ($stats['dept_approvals'] > 0): ?>
                                <span class="badge bg-light text-dark ms-1"><?php echo $stats['dept_approvals']; ?></span>
                            <?php endif; ?>
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (has_role(['it_lead', 'admin'])): ?>
                    <div class="col-md-3 mb-2">
                        <a href="it_approvals.php" class="btn btn-warning w-100">
                            <i class="bi bi-check-circle me-2"></i>
                            IT Approvals
                            <?php if (($stats['it_approvals']??'0') > 0): ?>
                                <span class="badge bg-light text-dark ms-1"><?php echo $stats['it_approvals']??'0'; ?></span>
                            <?php endif; ?>
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="all_requests.php" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-folder me-2"></i>
                            All Requests
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Requests -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    Recent Requests
                </h5>
                <a href="<?php echo has_role(['it_lead', 'admin']) ? 'all_requests.php' : 'my_requests.php'; ?>" 
                   class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_requests)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <p class="text-muted mt-2">No requests found.</p>
                        <a href="submit_request.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            Submit Your First Request
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Request #</th>
                                    <th>Title</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_requests as $request): ?>
                                <tr>
                                    <td>
                                        <span class="request-number"><?php echo htmlspecialchars($request['request_number']); ?></span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;">
                                            <?php echo htmlspecialchars($request['title']); ?>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($request['category_name']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo get_status_badge_class($request['status']); ?>">
                                            <?php echo REQUEST_STATUSES[$request['status']]; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo get_priority_badge_class($request['priority']); ?>">
                                            <?php echo PRIORITY_LEVELS[$request['priority']]; ?>
                                        </span>
                                    </td>
                                    <td><?php echo format_date($request['created_at'], 'M j, Y'); ?></td>
                                    <td>
                                        <a href="view_request.php?id=<?php echo $request['id']; ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
