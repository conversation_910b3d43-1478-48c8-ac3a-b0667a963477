<?php
require_once '../config/config.php';

// Require admin role
require_role('admin');

$page_title = 'Reports';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Date range for reports
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); // First day of current month
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d'); // Today

// Requests by status
$query = "SELECT status, COUNT(*) as count FROM it_requests 
          WHERE created_at BETWEEN :start_date AND :end_date
          GROUP BY status ORDER BY count DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':start_date', $start_date);
$stmt->bindParam(':end_date', $end_date);
$stmt->execute();
$status_report = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Requests by category
$query = "SELECT c.name, c.type, COUNT(r.id) as count FROM request_categories c
          LEFT JOIN it_requests r ON c.id = r.category_id 
          AND r.created_at BETWEEN :start_date AND :end_date
          GROUP BY c.id, c.name, c.type ORDER BY count DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':start_date', $start_date);
$stmt->bindParam(':end_date', $end_date);
$stmt->execute();
$category_report = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Requests by department
$query = "SELECT d.name, COUNT(r.id) as count FROM departments d
          LEFT JOIN users u ON d.id = u.department_id
          LEFT JOIN it_requests r ON u.id = r.user_id 
          AND r.created_at BETWEEN :start_date AND :end_date
          GROUP BY d.id, d.name ORDER BY count DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':start_date', $start_date);
$stmt->bindParam(':end_date', $end_date);
$stmt->execute();
$department_report = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Requests by priority
$query = "SELECT priority, COUNT(*) as count FROM it_requests 
          WHERE created_at BETWEEN :start_date AND :end_date
          GROUP BY priority ORDER BY 
          CASE priority 
            WHEN 'urgent' THEN 1 
            WHEN 'high' THEN 2 
            WHEN 'medium' THEN 3 
            WHEN 'low' THEN 4 
          END";
$stmt = $db->prepare($query);
$stmt->bindParam(':start_date', $start_date);
$stmt->bindParam(':end_date', $end_date);
$stmt->execute();
$priority_report = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Average resolution time
$query = "SELECT AVG(DATEDIFF(actual_completion_date, created_at)) as avg_days
          FROM it_requests 
          WHERE status = 'completed' 
          AND actual_completion_date IS NOT NULL
          AND created_at BETWEEN :start_date AND :end_date";
$stmt = $db->prepare($query);
$stmt->bindParam(':start_date', $start_date);
$stmt->bindParam(':end_date', $end_date);
$stmt->execute();
$avg_resolution = $stmt->fetch(PDO::FETCH_ASSOC);

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-graph-up me-2"></i>
            Reports & Analytics
        </h1>
    </div>
</div>

<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="" class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="<?php echo htmlspecialchars($start_date); ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="<?php echo htmlspecialchars($end_date); ?>">
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search me-2"></i>
                            Generate Report
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stat-card">
            <div class="stat-number"><?php echo array_sum(array_column($status_report, 'count')); ?></div>
            <div class="stat-label">Total Requests</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card success">
            <div class="stat-number">
                <?php 
                $completed = array_filter($status_report, function($item) { return $item['status'] == 'completed'; });
                echo $completed[0]['count']??0;
                ?>
            </div>
            <div class="stat-label">Completed</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card warning">
            <div class="stat-number">
                <?php 
                $pending = array_filter($status_report, function($item) { 
                    return in_array($item['status'], ['pending', 'dept_approved', 'it_approved', 'in_progress']); 
                });
                echo array_sum(array_column($pending, 'count'));
                ?>
            </div>
            <div class="stat-label">In Progress</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stat-card danger">
            <div class="stat-number">
                <?php echo $avg_resolution['avg_days'] ? round($avg_resolution['avg_days'], 1) : 'N/A'; ?>
            </div>
            <div class="stat-label">Avg Resolution (Days)</div>
        </div>
    </div>
</div>

<!-- Reports Grid -->
<div class="row">
    <!-- Status Report -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-pie-chart me-2"></i>
                    Requests by Status
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($status_report)): ?>
                    <p class="text-muted text-center">No data available for selected period.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Status</th>
                                    <th class="text-end">Count</th>
                                    <th class="text-end">%</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $total = array_sum(array_column($status_report, 'count'));
                                foreach ($status_report as $item): 
                                    $percentage = $total > 0 ? round(($item['count'] / $total) * 100, 1) : 0;
                                ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-<?php echo get_status_badge_class($item['status']); ?>">
                                            <?php echo REQUEST_STATUSES[$item['status']]; ?>
                                        </span>
                                    </td>
                                    <td class="text-end"><?php echo $item['count']; ?></td>
                                    <td class="text-end"><?php echo $percentage; ?>%</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Priority Report -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Requests by Priority
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($priority_report)): ?>
                    <p class="text-muted text-center">No data available for selected period.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Priority</th>
                                    <th class="text-end">Count</th>
                                    <th class="text-end">%</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $total = array_sum(array_column($priority_report, 'count'));
                                foreach ($priority_report as $item): 
                                    $percentage = $total > 0 ? round(($item['count'] / $total) * 100, 1) : 0;
                                ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-<?php echo get_priority_badge_class($item['priority']); ?>">
                                            <?php echo PRIORITY_LEVELS[$item['priority']]; ?>
                                        </span>
                                    </td>
                                    <td class="text-end"><?php echo $item['count']; ?></td>
                                    <td class="text-end"><?php echo $percentage; ?>%</td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Category Report -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-tags me-2"></i>
                    Requests by Category
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Type</th>
                                <th class="text-end">Count</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($category_report as $item): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($item['name']); ?></td>
                                <td>
                                    <span class="badge bg-secondary">
                                        <?php echo ucfirst($item['type']); ?>
                                    </span>
                                </td>
                                <td class="text-end"><?php echo $item['count']; ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Department Report -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-building me-2"></i>
                    Requests by Department
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Department</th>
                                <th class="text-end">Count</th>
                                <th class="text-end">%</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $total = array_sum(array_column($department_report, 'count'));
                            foreach ($department_report as $item): 
                                $percentage = $total > 0 ? round(($item['count'] / $total) * 100, 1) : 0;
                            ?>
                            <tr>
                                <td><?php echo htmlspecialchars($item['name']); ?></td>
                                <td class="text-end"><?php echo $item['count']; ?></td>
                                <td class="text-end"><?php echo $percentage; ?>%</td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
