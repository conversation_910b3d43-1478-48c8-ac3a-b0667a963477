<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Application settings
define('APP_NAME', 'IT Request Form System');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'https://uat.1-to-all.com/ITRForm');

// File upload settings
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif']);

// Email settings (configure according to your SMTP server)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'IT Request System');

// Pagination settings
define('RECORDS_PER_PAGE', 10);

// Request number format
define('REQUEST_NUMBER_PREFIX', 'REQ-');
define('REQUEST_NUMBER_FORMAT', 'Y-');

// Status definitions
define('REQUEST_STATUSES', [
    'pending' => 'Pending Approval',
    'dept_approved' => 'Department Approved',
    'it_approved' => 'IT Approved',
    'in_progress' => 'In Progress',
    'completed' => 'Completed',
    'rejected' => 'Rejected',
    'cancelled' => 'Cancelled'
]);

define('PRIORITY_LEVELS', [
    'low' => 'Low',
    'medium' => 'Medium',
    'high' => 'High',
    'urgent' => 'Urgent'
]);

define('USER_ROLES', [
    'employee' => 'Employee',
    'department_head' => 'Department Head',
    'it_lead' => 'IT Lead',
    'admin' => 'Administrator'
]);

// Include required files
require_once 'database.php';

// Utility functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirect($url) {
    header("Location: " . $url);
    exit();
}

function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id'] && isset($_SESSION['user_role']));
}

function require_login() {
    if (!is_logged_in()) {
        redirect('login.php');
    }
}

function has_role($required_roles) {
    if (!is_logged_in()) {
        return false;
    }
    
    if (is_string($required_roles)) {
        $required_roles = [$required_roles];
    }
    
    return in_array($_SESSION['user_role'], $required_roles);
}

function require_role($required_roles) {
    if (!has_role($required_roles)) {
        redirect('unauthorized.php');
    }
}

function format_date($date, $format = 'Y-m-d H:i:s') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

function get_status_badge_class($status) {
    $classes = [
        'pending' => 'warning',
        'dept_approved' => 'info',
        'it_approved' => 'primary',
        'in_progress' => 'secondary',
        'completed' => 'success',
        'rejected' => 'danger',
        'cancelled' => 'dark'
    ];
    
    return isset($classes[$status]) ? $classes[$status] : 'secondary';
}

function get_priority_badge_class($priority) {
    $classes = [
        'low' => 'success',
        'medium' => 'warning',
        'high' => 'danger',
        'urgent' => 'danger'
    ];
    
    return isset($classes[$priority]) ? $classes[$priority] : 'secondary';
}

function generate_request_number() {
    return REQUEST_NUMBER_PREFIX . date(REQUEST_NUMBER_FORMAT) . str_pad(rand(1, 9999), 3, '0', STR_PAD_LEFT);
}

// Error handling
function handle_error($message, $redirect_url = null) {
    $_SESSION['error_message'] = $message;
    if ($redirect_url) {
        redirect($redirect_url);
    }
}

function handle_success($message, $redirect_url = null) {
    $_SESSION['success_message'] = $message;
    if ($redirect_url) {
        redirect($redirect_url);
    }
}

function display_messages() {
    $html = '';
    
    if (isset($_SESSION['error_message'])) {
        $html .= '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
        $html .= htmlspecialchars($_SESSION['error_message']);
        $html .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        $html .= '</div>';
        unset($_SESSION['error_message']);
    }
    
    if (isset($_SESSION['success_message'])) {
        $html .= '<div class="alert alert-success alert-dismissible fade show" role="alert">';
        $html .= htmlspecialchars($_SESSION['success_message']);
        $html .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        $html .= '</div>';
        unset($_SESSION['success_message']);
    }
    
    return $html;
}
?>
