<?php
require_once 'config/config.php';
require_once 'classes/Request.php';
require_once 'classes/Category.php';

// Require login
require_login();

$page_title = 'Submit New Request';
$error_message = '';
$success_message = '';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Get categories
$category = new Category($db);
$categories = $category->getActive();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = sanitize_input($_POST['title']);
    $description = sanitize_input($_POST['description']);
    $category_id = (int)$_POST['category_id'];
    $priority = sanitize_input($_POST['priority']);
    $requested_completion_date = !empty($_POST['requested_completion_date']) ? $_POST['requested_completion_date'] : null;
    $estimated_cost = !empty($_POST['estimated_cost']) ? (float)$_POST['estimated_cost'] : null;
    
    // Validation
    if (empty($title)) {
        $error_message = 'Please enter a request title.';
    } elseif (empty($description)) {
        $error_message = 'Please enter a request description.';
    } elseif (empty($category_id)) {
        $error_message = 'Please select a category.';
    } elseif (!in_array($priority, ['low', 'medium', 'high', 'urgent'])) {
        $error_message = 'Please select a valid priority level.';
    } else {
        // Create request
        $request = new Request($db);
        $request->request_number = $request->generateRequestNumber();
        $request->user_id = $_SESSION['user_id'];
        $request->category_id = $category_id;
        $request->title = $title;
        $request->description = $description;
        $request->priority = $priority;
        $request->requested_completion_date = $requested_completion_date;
        $request->estimated_cost = $estimated_cost;
        
        if ($request->create()) {
            // Handle file uploads if any
            if (!empty($_FILES['attachments']['name'][0])) {
                $upload_result = handleFileUploads($request->id, $_FILES['attachments']);
                if (!$upload_result['success']) {
                    $error_message = $upload_result['message'];
                }
            }
            
            if (empty($error_message)) {
                handle_success('Request submitted successfully! Request number: ' . $request->request_number, 'my_requests.php');
            }
        } else {
            $error_message = 'Failed to submit request. Please try again.';
        }
    }
}

// File upload handler
function handleFileUploads($request_id, $files) {
    global $db;
    
    $upload_dir = UPLOAD_DIR . 'requests/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $uploaded_files = [];
    $errors = [];
    
    for ($i = 0; $i < count($files['name']); $i++) {
        if ($files['error'][$i] == UPLOAD_ERR_OK) {
            $original_name = $files['name'][$i];
            $file_size = $files['size'][$i];
            $file_type = $files['type'][$i];
            $tmp_name = $files['tmp_name'][$i];
            
            // Validate file
            $file_ext = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
            if (!in_array($file_ext, ALLOWED_FILE_TYPES)) {
                $errors[] = "File type not allowed: $original_name";
                continue;
            }
            
            if ($file_size > MAX_FILE_SIZE) {
                $errors[] = "File too large: $original_name";
                continue;
            }
            
            // Generate unique filename
            $filename = uniqid() . '_' . time() . '.' . $file_ext;
            $file_path = $upload_dir . $filename;
            
            if (move_uploaded_file($tmp_name, $file_path)) {
                // Save to database
                $query = "INSERT INTO request_attachments 
                          SET request_id = :request_id, filename = :filename, 
                              original_filename = :original_filename, file_path = :file_path,
                              file_size = :file_size, mime_type = :mime_type, uploaded_by = :uploaded_by";
                
                $stmt = $db->prepare($query);
                $stmt->bindParam(':request_id', $request_id);
                $stmt->bindParam(':filename', $filename);
                $stmt->bindParam(':original_filename', $original_name);
                $stmt->bindParam(':file_path', $file_path);
                $stmt->bindParam(':file_size', $file_size);
                $stmt->bindParam(':mime_type', $file_type);
                $stmt->bindParam(':uploaded_by', $_SESSION['user_id']);
                
                if ($stmt->execute()) {
                    $uploaded_files[] = $original_name;
                } else {
                    $errors[] = "Failed to save file info: $original_name";
                    unlink($file_path); // Remove uploaded file
                }
            } else {
                $errors[] = "Failed to upload file: $original_name";
            }
        }
    }
    
    if (!empty($errors)) {
        return ['success' => false, 'message' => implode(', ', $errors)];
    }
    
    return ['success' => true, 'files' => $uploaded_files];
}

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-plus-circle me-2"></i>
            Submit New Request
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-form me-2"></i>
                    Request Details
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label required">Request Title</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="title" 
                                   name="title" 
                                   value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>"
                                   placeholder="Brief description of your request"
                                   required>
                            <div class="invalid-feedback">
                                Please enter a request title.
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="priority" class="form-label required">Priority</label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="">Select Priority</option>
                                <?php foreach (PRIORITY_LEVELS as $key => $label): ?>
                                    <option value="<?php echo $key; ?>" 
                                            <?php echo (isset($_POST['priority']) && $_POST['priority'] == $key) ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a priority level.
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label required">Category</label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php 
                                $current_type = '';
                                foreach ($categories as $cat): 
                                    if ($cat['type'] != $current_type):
                                        if ($current_type != '') echo '</optgroup>';
                                        echo '<optgroup label="' . ucfirst($cat['type']) . '">';
                                        $current_type = $cat['type'];
                                    endif;
                                ?>
                                    <option value="<?php echo $cat['id']; ?>" 
                                            <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $cat['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                                <?php if ($current_type != '') echo '</optgroup>'; ?>
                            </select>
                            <div class="invalid-feedback">
                                Please select a category.
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="requested_completion_date" class="form-label">Requested Completion Date</label>
                            <input type="date" 
                                   class="form-control" 
                                   id="requested_completion_date" 
                                   name="requested_completion_date"
                                   value="<?php echo isset($_POST['requested_completion_date']) ? $_POST['requested_completion_date'] : ''; ?>"
                                   min="<?php echo date('Y-m-d'); ?>">
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="estimated_cost" class="form-label">Estimated Cost ($)</label>
                            <input type="number" 
                                   class="form-control" 
                                   id="estimated_cost" 
                                   name="estimated_cost"
                                   value="<?php echo isset($_POST['estimated_cost']) ? $_POST['estimated_cost'] : ''; ?>"
                                   step="0.01"
                                   min="0"
                                   placeholder="0.00">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label required">Description</label>
                        <textarea class="form-control"
                                  id="description"
                                  name="description"
                                  rows="5"
                                  placeholder="Please provide detailed information about your request..."
                                  required><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                        <div class="invalid-feedback">
                            Please enter a detailed description.
                        </div>
                        <div class="form-text">
                            Include as much detail as possible to help us process your request efficiently.
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="attachments" class="form-label">Attachments</label>
                        <div class="file-upload-area" onclick="document.getElementById('attachments').click();">
                            <i class="bi bi-cloud-upload display-4 text-muted"></i>
                            <p class="mt-2 mb-1">Click to upload files or drag and drop</p>
                            <small class="text-muted">
                                Allowed types: <?php echo implode(', ', ALLOWED_FILE_TYPES); ?>
                                (Max size: <?php echo number_format(MAX_FILE_SIZE / 1024 / 1024, 1); ?>MB per file)
                            </small>
                        </div>
                        <input type="file"
                               class="form-control d-none"
                               id="attachments"
                               name="attachments[]"
                               multiple
                               accept=".<?php echo implode(',.', ALLOWED_FILE_TYPES); ?>">
                        <div id="file-list" class="mt-2"></div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="bi bi-arrow-left me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-send me-2"></i>
                            Submit Request
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Request Guidelines
                </h6>
            </div>
            <div class="card-body">
                <h6>Priority Levels:</h6>
                <ul class="list-unstyled">
                    <li><span class="badge bg-success me-2">Low</span> Non-urgent requests</li>
                    <li><span class="badge bg-warning me-2">Medium</span> Standard business needs</li>
                    <li><span class="badge bg-danger me-2">High</span> Important for operations</li>
                    <li><span class="badge bg-danger me-2">Urgent</span> Critical/blocking issues</li>
                </ul>

                <hr>

                <h6>Approval Process:</h6>
                <ol class="small">
                    <li>Request submitted</li>
                    <li>Department head approval</li>
                    <li>IT lead approval</li>
                    <li>Implementation</li>
                    <li>Completion</li>
                </ol>

                <hr>

                <h6>Tips for Better Requests:</h6>
                <ul class="small">
                    <li>Be specific and detailed</li>
                    <li>Include business justification</li>
                    <li>Attach relevant documents</li>
                    <li>Set realistic timelines</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
