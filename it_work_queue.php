<?php
require_once 'config/config.php';
require_once 'classes/Request.php';
require_once 'classes/Category.php';

// Require IT helpdesk, IT lead, or admin role
require_role(['it_helpdesk', 'it_lead', 'admin']);

$page_title = 'IT Work Queue';
$error_message = '';
$success_message = '';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $request_id = (int)$_POST['request_id'];
    $action = $_POST['action'];
    $comments = sanitize_input($_POST['comments'] ?? '');
    
    if (in_array($action, ['start_work', 'complete_work'])) {
        $request_obj = new Request($db);
        $request_obj->id = $request_id;
        
        // Verify request exists and user can update it
        $request = $request_obj->getById($request_id);
        if ($request) {
            $new_status = '';
            $status_comment = '';
            
            if ($action == 'start_work' && $request['status'] == 'it_approved') {
                $new_status = 'in_progress';
                $status_comment = 'Work started by ' . $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];
                if (!empty($comments)) {
                    $status_comment .= ': ' . $comments;
                }
            } elseif ($action == 'complete_work' && $request['status'] == 'in_progress') {
                $new_status = 'completed';
                $status_comment = 'Work completed by ' . $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];
                if (!empty($comments)) {
                    $status_comment .= ': ' . $comments;
                }
                
                // Set actual completion date
                $query = "UPDATE it_requests SET actual_completion_date = CURRENT_DATE WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $request_id);
                $stmt->execute();
            }
            
            if (!empty($new_status)) {
                if ($request_obj->updateStatus($new_status, $_SESSION['user_id'], $status_comment)) {
                    $success_message = 'Request status updated successfully.';
                } else {
                    $error_message = 'Failed to update request status.';
                }
            } else {
                $error_message = 'Invalid status transition.';
            }
        } else {
            $error_message = 'Request not found.';
        }
    }
}

// Get work queue requests
$request_obj = new Request($db);

// Get IT approved requests (ready to start)
$query = "SELECT r.*, u.first_name, u.last_name, u.email,
                 c.name as category_name, c.type as category_type,
                 d.name as department_name
          FROM it_requests r
          JOIN users u ON r.user_id = u.id
          JOIN request_categories c ON r.category_id = c.id
          LEFT JOIN departments d ON u.department_id = d.id
          WHERE r.status = 'it_approved'
          ORDER BY r.priority DESC, r.created_at ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$ready_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get in progress requests
$query = "SELECT r.*, u.first_name, u.last_name, u.email,
                 c.name as category_name, c.type as category_type,
                 d.name as department_name
          FROM it_requests r
          JOIN users u ON r.user_id = u.id
          JOIN request_categories c ON r.category_id = c.id
          LEFT JOIN departments d ON u.department_id = d.id
          WHERE r.status = 'in_progress'
          ORDER BY r.priority DESC, r.created_at ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$in_progress_requests = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-list-task me-2"></i>
                IT Work Queue
            </h1>
            <div>
                <a href="all_requests.php" class="btn btn-outline-secondary">
                    <i class="bi bi-list me-2"></i>
                    All Requests
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card border-start-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-muted mb-1">Ready to Start</h6>
                        <h4 class="mb-0"><?php echo count($ready_requests); ?></h4>
                    </div>
                    <div class="text-primary">
                        <i class="bi bi-play-circle fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-start-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-muted mb-1">In Progress</h6>
                        <h4 class="mb-0"><?php echo count($in_progress_requests); ?></h4>
                    </div>
                    <div class="text-warning">
                        <i class="bi bi-gear fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (!empty($error_message)): ?>
    <div class="alert alert-danger" role="alert">
        <?php echo htmlspecialchars($error_message); ?>
    </div>
<?php endif; ?>

<?php if (!empty($success_message)): ?>
    <div class="alert alert-success" role="alert">
        <?php echo htmlspecialchars($success_message); ?>
    </div>
<?php endif; ?>

<!-- Ready to Start Requests -->
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-play-circle me-2"></i>
                    Ready to Start (<?php echo count($ready_requests); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($ready_requests)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-check-circle text-success fs-1"></i>
                        <p class="text-muted mt-2">No requests ready to start. Great job!</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($ready_requests as $request): ?>
                        <div class="col-lg-6 mb-3">
                            <div class="card border-start-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0">
                                            <span class="request-number me-2"><?php echo htmlspecialchars($request['request_number']); ?></span>
                                        </h6>
                                        <span class="badge bg-<?php echo get_priority_badge_class($request['priority']); ?>">
                                            <?php echo PRIORITY_LEVELS[$request['priority']]; ?>
                                        </span>
                                    </div>
                                    
                                    <h6 class="mb-2"><?php echo htmlspecialchars($request['title']); ?></h6>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <strong>Requester:</strong> <?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?><br>
                                            <strong>Department:</strong> <?php echo htmlspecialchars($request['department_name']); ?><br>
                                            <strong>Category:</strong> <?php echo htmlspecialchars($request['category_name']); ?><br>
                                            <strong>Submitted:</strong> <?php echo format_date($request['created_at'], 'M j, Y'); ?>
                                        </small>
                                    </div>
                                    
                                    <p class="card-text small text-muted mb-3">
                                        <?php echo htmlspecialchars(substr($request['description'], 0, 100)) . (strlen($request['description']) > 100 ? '...' : ''); ?>
                                    </p>
                                    
                                    <form method="POST" action="" class="mb-2">
                                        <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                        
                                        <div class="mb-2">
                                            <textarea class="form-control form-control-sm" 
                                                      name="comments" 
                                                      placeholder="Optional: Add work notes..."
                                                      rows="2"></textarea>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <button type="submit" 
                                                    name="action" 
                                                    value="start_work" 
                                                    class="btn btn-primary btn-sm"
                                                    onclick="return confirm('Start working on this request?')">
                                                <i class="bi bi-play-fill me-2"></i>
                                                Start Work
                                            </button>
                                        </div>
                                    </form>
                                    
                                    <div class="d-grid">
                                        <a href="view_request.php?id=<?php echo $request['id']; ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-eye me-2"></i>
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- In Progress Requests -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>
                    In Progress (<?php echo count($in_progress_requests); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($in_progress_requests)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox text-muted fs-1"></i>
                        <p class="text-muted mt-2">No requests currently in progress.</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($in_progress_requests as $request): ?>
                        <div class="col-lg-6 mb-3">
                            <div class="card border-start-warning">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0">
                                            <span class="request-number me-2"><?php echo htmlspecialchars($request['request_number']); ?></span>
                                        </h6>
                                        <span class="badge bg-<?php echo get_priority_badge_class($request['priority']); ?>">
                                            <?php echo PRIORITY_LEVELS[$request['priority']]; ?>
                                        </span>
                                    </div>
                                    
                                    <h6 class="mb-2"><?php echo htmlspecialchars($request['title']); ?></h6>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <strong>Requester:</strong> <?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?><br>
                                            <strong>Department:</strong> <?php echo htmlspecialchars($request['department_name']); ?><br>
                                            <strong>Category:</strong> <?php echo htmlspecialchars($request['category_name']); ?><br>
                                            <strong>Started:</strong> <?php echo format_date($request['updated_at'], 'M j, Y'); ?>
                                        </small>
                                    </div>
                                    
                                    <p class="card-text small text-muted mb-3">
                                        <?php echo htmlspecialchars(substr($request['description'], 0, 100)) . (strlen($request['description']) > 100 ? '...' : ''); ?>
                                    </p>
                                    
                                    <form method="POST" action="" class="mb-2">
                                        <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                        
                                        <div class="mb-2">
                                            <textarea class="form-control form-control-sm" 
                                                      name="comments" 
                                                      placeholder="Optional: Add completion notes..."
                                                      rows="2"></textarea>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <button type="submit" 
                                                    name="action" 
                                                    value="complete_work" 
                                                    class="btn btn-success btn-sm"
                                                    onclick="return confirm('Mark this request as completed?')">
                                                <i class="bi bi-check-lg me-2"></i>
                                                Complete Work
                                            </button>
                                        </div>
                                    </form>
                                    
                                    <div class="d-grid">
                                        <a href="view_request.php?id=<?php echo $request['id']; ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-eye me-2"></i>
                                            View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
