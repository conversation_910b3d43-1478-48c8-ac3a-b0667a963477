-- Sample data for IT Request Form System
USE ITRForm;

-- Insert departments
INSERT INTO departments (name, description) VALUES
('Human Resources', 'Human Resources Department'),
('Finance', 'Finance and Accounting Department'),
('Marketing', 'Marketing and Sales Department'),
('Operations', 'Operations and Production Department'),
('IT', 'Information Technology Department'),
('Administration', 'General Administration');

-- Insert sample users (password is 'password123' hashed with PHP password_hash)
INSERT INTO users (username, email, password_hash, first_name, last_name, department_id, role) VALUES
-- Admin user
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', 5, 'admin'),

-- IT Lead
('it_lead', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<PERSON>', '<PERSON>', 5, 'it_lead'),

-- Department Heads
('hr_head', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah', 'Johnson', 1, 'department_head'),
('finance_head', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Michael', 'Brown', 2, 'department_head'),
('marketing_head', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Emily', 'Davis', 3, 'department_head'),
('ops_head', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'David', 'Wilson', 4, 'department_head'),

-- Regular employees
('jdoe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Doe', 1, 'employee'),
('asmith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Alice', 'Smith', 2, 'employee'),
('bwilson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Bob', 'Wilson', 3, 'employee'),
('cjones', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Carol', 'Jones', 4, 'employee');

-- Insert request categories
INSERT INTO request_categories (name, type, description) VALUES
('Software Installation', 'software', 'Request for new software installation or updates'),
('Software License', 'software', 'Request for software license or renewal'),
('Hardware Request', 'hardware', 'Request for new hardware equipment'),
('Hardware Repair', 'hardware', 'Request for hardware repair or replacement'),
('Network Access', 'access', 'Request for network access or VPN setup'),
('Email Account', 'access', 'Request for email account creation or modification'),
('System Maintenance', 'maintenance', 'Request for system maintenance or updates'),
('Training', 'other', 'Request for IT training or support'),
('Data Recovery', 'other', 'Request for data recovery services'),
('Security Issue', 'other', 'Report security issues or request security measures');

-- Insert sample IT requests
INSERT INTO it_requests (request_number, user_id, category_id, title, description, priority, status, requested_completion_date) VALUES
('REQ-2024-001', 7, 1, 'Install Adobe Creative Suite', 'Need Adobe Creative Suite for marketing materials creation', 'medium', 'pending', '2024-09-15'),
('REQ-2024-002', 8, 3, 'New Laptop Request', 'Current laptop is 5 years old and running slowly. Need replacement for daily work.', 'high', 'dept_approved', '2024-09-10'),
('REQ-2024-003', 9, 5, 'VPN Access Setup', 'Need VPN access for remote work capabilities', 'medium', 'it_approved', '2024-09-05'),
('REQ-2024-004', 10, 2, 'Microsoft Office License', 'Need additional Microsoft Office license for new employee', 'low', 'in_progress', '2024-09-20'),
('REQ-2024-005', 7, 4, 'Printer Repair', 'Office printer is jamming frequently and needs repair', 'medium', 'completed', '2024-08-30');

-- Insert sample approvals
INSERT INTO request_approvals (request_id, approver_id, approval_type, status, comments, approved_at) VALUES
(2, 4, 'department_head', 'approved', 'Approved - employee needs new equipment for productivity', '2024-08-27 10:30:00'),
(3, 5, 'department_head', 'approved', 'Approved for remote work setup', '2024-08-26 14:15:00'),
(3, 2, 'it_lead', 'approved', 'VPN access configured and ready', '2024-08-27 09:00:00'),
(4, 4, 'department_head', 'approved', 'New employee onboarding requirement', '2024-08-25 16:20:00'),
(4, 2, 'it_lead', 'approved', 'License procurement in progress', '2024-08-26 11:45:00'),
(5, 3, 'department_head', 'approved', 'Printer is essential for daily operations', '2024-08-20 13:30:00'),
(5, 2, 'it_lead', 'approved', 'Repair completed successfully', '2024-08-22 15:00:00');

-- Insert sample status history
INSERT INTO request_status_history (request_id, old_status, new_status, changed_by, comments) VALUES
(1, NULL, 'pending', 7, 'Request submitted'),
(2, NULL, 'pending', 8, 'Request submitted'),
(2, 'pending', 'dept_approved', 4, 'Approved by department head'),
(3, NULL, 'pending', 9, 'Request submitted'),
(3, 'pending', 'dept_approved', 5, 'Approved by department head'),
(3, 'dept_approved', 'it_approved', 2, 'Approved by IT lead'),
(4, NULL, 'pending', 10, 'Request submitted'),
(4, 'pending', 'dept_approved', 4, 'Approved by department head'),
(4, 'dept_approved', 'it_approved', 2, 'Approved by IT lead'),
(4, 'it_approved', 'in_progress', 2, 'License procurement started'),
(5, NULL, 'pending', 7, 'Request submitted'),
(5, 'pending', 'dept_approved', 3, 'Approved by department head'),
(5, 'dept_approved', 'it_approved', 2, 'Approved by IT lead'),
(5, 'it_approved', 'in_progress', 2, 'Repair work started'),
(5, 'in_progress', 'completed', 2, 'Repair completed successfully');

-- Insert sample comments
INSERT INTO request_comments (request_id, user_id, comment, is_internal) VALUES
(2, 8, 'This is urgent as my current laptop crashes frequently during presentations.', FALSE),
(2, 4, 'Approved. Please prioritize this request.', FALSE),
(3, 9, 'I will be working from home 3 days a week starting next month.', FALSE),
(3, 2, 'VPN credentials sent via secure email.', TRUE),
(4, 2, 'Checking with procurement for license availability.', TRUE),
(5, 7, 'Printer has been problematic for the past two weeks.', FALSE),
(5, 2, 'Repair completed. Printer is working normally now.', FALSE);

-- Insert sample notifications
INSERT INTO notifications (user_id, request_id, type, title, message, is_read) VALUES
(7, 1, 'request_submitted', 'Request Submitted', 'Your IT request REQ-2024-001 has been submitted successfully.', TRUE),
(3, 1, 'approval_needed', 'Approval Required', 'IT request REQ-2024-001 requires your approval.', FALSE),
(8, 2, 'request_approved', 'Request Approved', 'Your IT request REQ-2024-002 has been approved by your department head.', TRUE),
(2, 2, 'approval_needed', 'IT Approval Required', 'IT request REQ-2024-002 requires IT lead approval.', FALSE),
(9, 3, 'request_completed', 'Request Completed', 'Your IT request REQ-2024-003 has been completed.', FALSE);
