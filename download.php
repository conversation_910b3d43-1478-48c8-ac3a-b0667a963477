<?php
require_once 'config/config.php';

// Require login
require_login();

$attachment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (empty($attachment_id)) {
    handle_error('Invalid attachment ID.', 'dashboard.php');
}

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Get attachment details
$query = "SELECT a.*, r.user_id FROM request_attachments a
          JOIN it_requests r ON a.request_id = r.id
          WHERE a.id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $attachment_id);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    handle_error('Attachment not found.', 'dashboard.php');
}

$attachment = $stmt->fetch(PDO::FETCH_ASSOC);

// Check if user can download this attachment
$can_download = false;
if ($attachment['user_id'] == $_SESSION['user_id']) {
    $can_download = true; // Owner can download
} elseif (has_role(['admin', 'it_lead'])) {
    $can_download = true; // Admin and IT lead can download all
} elseif (has_role('department_head')) {
    // Department head can download if from their department
    $query = "SELECT u.department_id FROM users u
              JOIN it_requests r ON u.id = r.user_id
              WHERE r.id = :request_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':request_id', $attachment['request_id']);
    $stmt->execute();
    $request_dept = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($request_dept && $request_dept['department_id'] == $_SESSION['department_id']) {
        $can_download = true;
    }
}

if (!$can_download) {
    handle_error('You are not authorized to download this file.', 'dashboard.php');
}

// Check if file exists
$file_path = $attachment['file_path'];
if (!file_exists($file_path)) {
    handle_error('File not found on server.', 'dashboard.php');
}

// Set headers for download
header('Content-Type: ' . $attachment['mime_type']);
header('Content-Disposition: attachment; filename="' . $attachment['original_filename'] . '"');
header('Content-Length: ' . $attachment['file_size']);
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// Output file
readfile($file_path);
exit();
?>
