<?php
require_once 'config/config.php';
require_once 'classes/Request.php';
require_once 'classes/Approval.php';

// Require IT lead or admin role
require_role(['it_lead', 'admin']);

$page_title = 'IT Approvals';
$error_message = '';
$success_message = '';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Handle approval actions
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    $request_id = (int)$_POST['request_id'];
    $action = $_POST['action'];
    $comments = sanitize_input($_POST['comments']);

   // var_dump($_POST);
    
    if (in_array($action, ['approved', 'rejected'])) {
        $approval = new Approval($db);
        
        if ($approval->canUserApprove($_SESSION['user_id'], $_SESSION['user_role'], $request_id)) {
            if ($approval->processITApproval($request_id, $_SESSION['user_id'], $action, $comments)) {
                $success_message = 'Request ' . $action . ' successfully.';
            } else {
                $error_message = 'Failed to process approval. Please try again.';
            }
        } else {
            $error_message = 'You are not authorized to approve this request.';
        }
    }
}

//echo "<p>DEBUG:</p>";
/*
$query = "SELECT * FROM it_requests";
$stmt = $db->prepare($query);
$stmt->execute();
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);
var_dump($result);
*/

// Get pending approvals
$approval = new Approval($db);
$pending_requests = $approval->getPendingApprovalsForUser($_SESSION['user_id'], $_SESSION['user_role']);

include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="bi bi-gear-wide-connected me-2"></i>
            IT Approvals
        </h1>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-start-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-muted mb-1">Pending IT Approvals</h6>
                        <h4 class="mb-0"><?php echo count($pending_requests); ?></h4>
                    </div>
                    <div class="text-primary">
                        <i class="bi bi-gear fs-2"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pending Requests -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-check me-2"></i>
                    Requests Awaiting IT Approval
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        <i class="bi bi-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (empty($pending_requests)): ?>
                    <div class="text-center py-5">
                        <i class="bi bi-check-all display-1 text-success"></i>
                        <h4 class="text-muted mt-3">All caught up!</h4>
                        <p class="text-muted">No requests are waiting for IT approval.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($pending_requests as $request): ?>
                    <div class="card mb-3 request-card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-0">
                                        <span class="request-number me-2"><?php echo htmlspecialchars($request['request_number']); ?></span>
                                        <?php echo htmlspecialchars($request['title']); ?>
                                    </h6>
                                    <small class="text-muted">
                                        Submitted by <?php echo htmlspecialchars($request['first_name'] . ' ' . $request['last_name']); ?>
                                        (<?php echo htmlspecialchars($request['department_name']); ?>)
                                        on <?php echo format_date($request['created_at'], 'M j, Y g:i A'); ?>
                                    </small>
                                </div>
                                <div class="col-md-4 text-md-end">
                                    <span class="badge bg-<?php echo get_priority_badge_class($request['priority']); ?> me-2">
                                        <?php echo PRIORITY_LEVELS[$request['priority']]; ?>
                                    </span>
                                    <span class="badge bg-secondary">
                                        <?php echo htmlspecialchars($request['category_name']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>Description:</h6>
                                    <p class="text-muted"><?php echo nl2br(htmlspecialchars($request['description'])); ?></p>
                                    
                                    <div class="row">
                                        <?php if (!empty($request['requested_completion_date'])): ?>
                                        <div class="col-md-6">
                                            <p class="mb-1">
                                                <strong>Requested Completion:</strong> 
                                                <?php echo format_date($request['requested_completion_date'], 'M j, Y'); ?>
                                            </p>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($request['estimated_cost'])): ?>
                                        <div class="col-md-6">
                                            <p class="mb-1">
                                                <strong>Estimated Cost:</strong> 
                                                $<?php echo number_format($request['estimated_cost'], 2); ?>
                                            </p>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Department Approval Info -->
                                    <?php
                                    $dept_approval = $approval->getByRequestAndType($request['id'], 'department_head');
                                    if ($dept_approval):
                                    ?>
                                    <div class="alert alert-success mt-3">
                                        <h6 class="alert-heading">
                                            <i class="bi bi-check-circle me-2"></i>
                                            Department Approved
                                        </h6>
                                        <p class="mb-1">
                                            <strong>Approved by:</strong> <?php echo htmlspecialchars($dept_approval['first_name'] . ' ' . $dept_approval['last_name']); ?>
                                        </p>
                                        <p class="mb-1">
                                            <strong>Date:</strong> <?php echo format_date($dept_approval['approved_at'], 'M j, Y g:i A'); ?>
                                        </p>
                                        <?php if (!empty($dept_approval['comments'])): ?>
                                        <p class="mb-0">
                                            <strong>Comments:</strong> <?php echo htmlspecialchars($dept_approval['comments']); ?>
                                        </p>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-4">
                                    <div class="border rounded p-3 bg-light">
                                        <h6 class="mb-3">IT Approval Decision</h6>
                                        
                                        <form method="POST" action="" class="approval-form">
                                            <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                            
                                            <div class="mb-3">
                                                <label for="comments_<?php echo $request['id']; ?>" class="form-label">Comments</label>
                                                <textarea class="form-control" 
                                                          id="comments_<?php echo $request['id']; ?>" 
                                                          name="comments" 
                                                          rows="3" 
                                                          placeholder="Add your comments (optional)"></textarea>
                                            </div>
                                            
                                            <div class="d-grid gap-2">
                                                <button type="submit" 
                                                        name="action" 
                                                        value="approved" 
                                                        class="btn btn-success"
                                                        onclick="return confirm('Are you sure you want to approve this request?')">
                                                    <i class="bi bi-check-lg me-2"></i>
                                                    Approve
                                                </button>
                                                <button type="submit" 
                                                        name="action" 
                                                        value="rejected" 
                                                        class="btn btn-danger"
                                                        onclick="return confirm('Are you sure you want to reject this request?')">
                                                    <i class="bi bi-x-lg me-2"></i>
                                                    Reject
                                                </button>
                                            </div>
                                        </form>
                                        
                                        <hr>
                                        
                                        <div class="d-grid">
                                            <a href="view_request.php?id=<?php echo $request['id']; ?>" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-eye me-2"></i>
                                                View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
